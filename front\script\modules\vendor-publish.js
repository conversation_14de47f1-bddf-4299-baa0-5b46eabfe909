const fs = require('fs');
const path = require('path');
const copy = require('recursive-copy');

const not_found = [];
const vendor_dir_list = [
  'tinymce',
];

const node_modules_dir = __dirname+'/../../node_modules/';
const public_vendor_dir = __dirname+'/../../src/static/vendor/';
const public_vendor_options_dir = __dirname+'/../../src/static/vendor-options/';

module.exports = () => {
  vendor_dir_list.forEach(async (vendor_dir) => {
    const original_dir = node_modules_dir + vendor_dir;
    const public_dir = public_vendor_dir + vendor_dir;
    const option_dir = public_vendor_options_dir + vendor_dir;

    copy(original_dir, public_dir, { overwrite: true, dot: true })
    if (fs.existsSync(option_dir)) copy(option_dir, public_dir, { overwrite: true, dot: true })
    // if (fs.existsSync(original_dir)) {
    //   copy(original_dir, public_dir, { overwrite: true, dot: true })
    // } else {
    //   not_found.push(original_dir)
    // }
  });

  // if( not_found.length > 0 ) {
  //   not_found.forEach((not_found_dir) => {
  //     console.log(`"${not_found_dir}" is not found.`)
  //   });
  // } else {
  //   console.log('vendor published succesfully.')
  // }
  console.log('vendor published succesfully.')
}