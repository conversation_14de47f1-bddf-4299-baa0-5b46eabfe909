<?xml version="1.0" encoding="UTF-8"?>
<svg id="_レイヤー_2" data-name="レイヤー 2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 64 64">
  <defs>
    <style>
      .cls-1 {
        fill: none;
      }

      .cls-1, .cls-2, .cls-3, .cls-4 {
        stroke-width: 0px;
      }

      .cls-2 {
        fill: #2e2f2e;
      }

      .cls-3 {
        fill: #fff;
      }

      .cls-4 {
        fill: #f5d334;
      }
    </style>
  </defs>
  <g id="icon-color">
    <g id="c-shake_hand">
      <g>
        <g>
          <path class="cls-3" d="m44.3,37.45l-15.08-15.08c-.75-.75-2.73-.87-2.73-.87,0,0-.38,0-.69,0h-15.5c-1.63,0-2.95,1.32-2.95,2.95v10.4c0,1.63,1.32,2.95,2.95,2.95h8.63l12.39,8.46c2.58,2.06,5.61,2.61,8.35,0l4.64-4.64c1.15-1.15,1.15-3.02,0-4.17Z"/>
          <path class="cls-2" d="m35.68,49.53c-1.73,0-3.51-.69-5.25-2.06l-11.97-8.17h-8.16c-2.45,0-4.45-2-4.45-4.45v-10.4c0-2.45,2-4.45,4.45-4.45h15.5c.28-.02.72,0,.72,0,.66.04,2.69.25,3.75,1.31l15.08,15.08c1.74,1.74,1.74,4.56,0,6.29l-4.64,4.64c-1.54,1.47-3.26,2.21-5.05,2.21Zm-9.43-26.53c-.1,0-.25,0-.37,0h-15.58c-.8,0-1.45.65-1.45,1.45v10.4c0,.8.65,1.45,1.45,1.45h9.09l12.87,8.79c2.34,1.87,4.48,1.9,6.37.09l4.61-4.61c.57-.57.57-1.49,0-2.05l-15.08-15.08c-.21-.17-1.06-.38-1.76-.43,0,0-.06,0-.15,0Z"/>
        </g>
        <g>
          <path class="cls-3" d="m49.06,38.7h3.69v-17.16h-18.74s-.5.5-1,1.01l-7.2,7.2c-.9.9-.75,2.72.15,3.62.9.9,2.84,1.17,3.74.27l5.32-5.32,10.09,10.39h3.96Z"/>
          <path class="cls-2" d="m54.24,40.2h-9.78l-9.48-9.75-4.24,4.24c-.73.73-1.85,1.09-3.07.97-1.08-.1-2.1-.55-2.79-1.24-1.54-1.54-1.61-4.28-.15-5.74l7.76-7.76c.09-.09.2-.21.33-.32l.04-.05h0c.38-.32.88-.62,1.43-.52h19.94s0,20.16,0,20.16Zm-8.51-3h5.51v-14.16h-16.62s0,0,0,0l-7.76,7.76c-.26.26-.22,1.12.15,1.5.15.15.51.33.95.38.38.04.62-.06.67-.11l6.39-6.39,10.71,11.03Z"/>
        </g>
        <g>
          <rect class="cls-4" x="49.66" y="19.44" width="8.98" height="20.85" rx="3" ry="3" transform="translate(-2.4 4.83) rotate(-5)"/>
          <path class="cls-2" d="m53.31,41.9c-2.31,0-4.28-1.77-4.48-4.11l-1.29-14.8c-.11-1.2.26-2.36,1.04-3.28.77-.92,1.86-1.49,3.05-1.59l2.97-.26c1.19-.1,2.36.26,3.29,1.04.92.77,1.49,1.86,1.59,3.06l1.29,14.8c.11,1.2-.26,2.36-1.04,3.28-.77.92-1.86,1.49-3.05,1.59l-2.97.26c-.13.01-.26.02-.39.02Zm1.68-21.06s-.09,0-.13,0l-2.97.26c-.4.04-.76.22-1.02.53-.26.31-.38.7-.34,1.09l1.29,14.8c.07.82.8,1.44,1.63,1.36l2.97-.26c.4-.04.76-.22,1.02-.53.26-.31.38-.7.34-1.09l-1.29-14.8c-.04-.4-.22-.76-.53-1.02-.27-.23-.61-.35-.96-.35Z"/>
        </g>
        <g>
          <rect class="cls-4" x="6.58" y="19.44" width="8.98" height="20.85" rx="3" ry="3" transform="translate(2.65 -.85) rotate(5)"/>
          <path class="cls-2" d="m11.91,41.9c-.13,0-.26,0-.39-.02h0l-2.97-.26c-2.47-.22-4.31-2.4-4.09-4.87l1.29-14.8c.1-1.2.67-2.28,1.59-3.06.92-.77,2.09-1.14,3.29-1.04l2.97.26c1.2.1,2.28.67,3.05,1.59.77.92,1.14,2.09,1.04,3.28l-1.29,14.8c-.21,2.34-2.18,4.11-4.48,4.11Zm-.13-3.01c.82.07,1.55-.54,1.63-1.36l1.29-14.8c.04-.4-.09-.79-.34-1.09-.26-.31-.62-.5-1.02-.53l-2.97-.26c-.39-.04-.79.09-1.09.34-.31.26-.5.62-.53,1.02l-1.29,14.8c-.07.82.54,1.55,1.36,1.62l2.97.26Z"/>
        </g>
        <g>
          <line class="cls-3" x1="42.97" y1="41.91" x2="37.84" y2="37.26"/>
          <path class="cls-2" d="m42.97,42.91c-.24,0-.48-.09-.67-.26l-5.13-4.65c-.41-.37-.44-1-.07-1.41.37-.41,1-.44,1.41-.07l5.13,4.65c.41.37.44,1,.07,1.41-.2.22-.47.33-.74.33Z"/>
        </g>
        <g>
          <line class="cls-3" x1="40.4" y1="44.24" x2="35.27" y2="39.58"/>
          <path class="cls-2" d="m40.4,45.24c-.24,0-.48-.09-.67-.26l-5.13-4.65c-.41-.37-.44-1-.07-1.41.37-.41,1-.44,1.41-.07l5.13,4.65c.41.37.44,1,.07,1.41-.2.22-.47.33-.74.33Z"/>
        </g>
        <g>
          <line class="cls-3" x1="37.84" y1="46.56" x2="32.71" y2="41.91"/>
          <path class="cls-2" d="m37.84,47.56c-.24,0-.48-.09-.67-.26l-5.13-4.65c-.41-.37-.44-1-.07-1.41.37-.41,1-.44,1.41-.07l5.13,4.65c.41.37.44,1,.07,1.41-.2.22-.47.33-.74.33Z"/>
        </g>
      </g>
      <rect class="cls-1" width="64" height="64"/>
    </g>
  </g>
</svg>