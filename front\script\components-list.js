/*
 * components/chamomile以下のコンポーネント階層をJSON出力する
 */
const fs = require('fs');

const src_dir = __dirname + '/../src';
const dir = src_dir + '/components/chamomile';

const checkDirectories = {
  statics: { hasType: true },
  dynamics: { hasType: true },
  system: { hasType: false },
}

const defaultSet = {
  label: 'TypeComponentName',
  type: 'Type', // Element / Trunk / Aggregation / Layout
  name: 'ComponentName',
  slug: 'component_slug',
  statics: 'none', // usable / none
  dynamics: 'none', // usable / none
};

// 出力用
const results = [];

// 現在処理中のdirGroup
let currentDirGroup = null;

// 現在処理中のtype
let currentType = null;

// 現在処理中のpath
let currentPathPrefix = null;

const toUpperCamelCase = (str) => str.charAt(0).toUpperCase() + str.slice(1);
const toLower = (str) => str.charAt(0).toLowerCase() + str.slice(1);

const getLabel = (parentDir, currentComponent) => {
  if( !currentType ) return [parentDir, currentComponent].join('').replace('.vue', '');
  return [toUpperCamelCase(currentType), parentDir, currentComponent].join('').replace('.vue', '');
}

const getType = () => {
  if( !currentType ) return toUpperCamelCase(currentDirGroup);
  return toUpperCamelCase(currentType);
}

const getParams = (path) => {
  const params = {};

  const vue = fs.readFileSync(path, 'utf-8');

  // const reg = new RegExp('const\s+meta\s');
  // const reg = new RegExp('const\\s+meta');
  // const paramLine = vue.match(/const\s*meta\s*[\s\S]*?}[^,]/);
  // if( !paramLine ) return {};
  // const paramString = paramLine[0].replace(/const\s*meta\s*=\s*/g, '').replace(/([^\s]+):/g, "\"$1\":").replace(/'/g, '"').replace(/\/\/.*\n/, '');

  params.status = getParam(vue, 'componentStatus');
  params.isSubComponent = getParam(vue, 'isSubComponent');
  params.componentIcon = getParam(vue, 'componentIcon');

  return params;
};

const getParam = (data, name) => {
  const reg = new RegExp(`${name}[:\s]+.*,`);

  const paramLine = data.match(reg);
  if( !paramLine ) return null;

  try {
    const paramString = '{"'+paramLine[0].replace(/,/g, '}').replace(/:/, '":').replace(/'/g, '"');
    const paramJson = JSON.parse(paramString);
    return paramJson[name] ?? null;

  } catch(e) {
    // Error handling
    console.log(e);
  }

  return null;
}

// 再起的に呼び出してコンポーネントに辿り着く関数
// const digDir = (parent, current) => {
//   if( !currentDirGroup ) return parent;

//   if (current.indexOf('.vue') < 0) {
//     // currentPathPrefix = [currentPathPrefix, current].join('/');
//     // console.log(currentPathPrefix)
//     // const dirPath = parent ? [currentPathPrefix, toLower(parent), current].join('/'): [currentPathPrefix, current].join('/');
//     console.log('current:'+current)
//     const subDirent = fs.readdirSync([currentPathPrefix, current].join('/'));
//     console.log('ディレクトリの場合:')
//     console.log(subDirent)
//     //
//     // const subDirent = fs.readdirSync([currentPathPrefix, current].join('/'));
//     subDirent.reduce(digDir, parent + '/' +current);
//     return '';
//   }
//   // console.log(current + '::' +parent)
//   console.log('パス構造::' +parent + '/' + current)
//   // console.log(parent)
//   // ここでコンポーネント名に変換？
//   // setResult(current, parent);
//   // currentPathPrefix = pathPrefix;
//   // ここではパス構造を返すべき
//   // 特に返す必要ない
//   return '';
// };

const digDir = (parent, list) => {
  list.forEach(listStr => {
    if (listStr.indexOf('.vue') < 0) {
      const subDirectly = fs.readdirSync([currentPathPrefix, parent, listStr].join('/'));
      digDir(parent+ '/' + listStr, subDirectly)
      return;
    }
    // ファイルの場合
    setResult(listStr, parent);
  })
}

// コンポーネントをobjectに整形・追加
const setResult = (currentComponent, parentDir = '') => {
  const name = currentComponent.replace('.vue', '');
  const slug = [...parentDir.toLowerCase().split('/'), name].filter(a => a).join('-');
  // const label = [toUpperCamelCase(currentType), parentDir, currentComponent].join('').replace('.vue', '');
  const label = getLabel(parentDir, currentComponent);

  const fullPath = currentPathPrefix+`/${slug.split('-').join('/')}.vue`;
  const { status, isSubComponent, componentIcon } = getParams(fullPath);

  const target = results.find(r => r.label === label);

  if (target) {
    target['has'+toUpperCamelCase(currentDirGroup)] = true;
    target[currentDirGroup+'Status'] = status ?? '作成済';
    target[currentDirGroup+'IsSubComponent'] = isSubComponent;
    if( !target.componentIcon ) target.componentIcon = componentIcon;
  }
  else {
    results.push({
      ...defaultSet,
      ['has'+toUpperCamelCase(currentDirGroup)]: true,
      type: getType(),
      label,
      name: parentDir + name,
      slug,
      componentIcon,
      [currentDirGroup+'Status']: status ?? '作成済',
      [currentDirGroup+'IsSubComponent']: isSubComponent,
    });
  }
}

Object.keys(checkDirectories).forEach(dirKey => {
  currentDirGroup = dirKey;
  currentType = null;
  const settings = checkDirectories[dirKey];

  if( settings.hasType ) {
    const dirList = fs.readdirSync(dir + '/' + currentDirGroup);
    dirList.forEach(type => {
      currentType = type;
      currentPathPrefix = [dir, currentDirGroup, currentType].join('/');
      const components = fs.readdirSync(currentPathPrefix);
  
      // components.reduce(digDir, '');
      digDir('', components)
    });  
  } else {
    currentPathPrefix = [dir, currentDirGroup].join('/');
    const components = fs.readdirSync(currentPathPrefix);
    // components.reduce(digDir, '');
    digDir('', components)
  }
});

const distPath = src_dir + '/master/chamomile/dist';
if (!fs.existsSync(distPath)) {
  fs.mkdirSync(distPath, { recursive: true });
}
fs.writeFileSync(distPath + '/components.json', JSON.stringify(results));
