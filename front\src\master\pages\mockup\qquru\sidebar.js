import config from './config';

const rootPath = config.rootPath;

export default [
  {
    name: 'adminCustomerMenu',
    type: 'mockupUserList',
    data: [],
  },
  {
    name: 'adminDeliveryMenu',
    label: '一斉配信',
    type: 'menuList',
    data: [
      {
        label: 'LINE',
        role: 'crm_line_message_messages',
        children: [
          {
            label: 'メッセージ配信',
            page: `${rootPath}/a/crm/line/list`,
            role: 'crm_line_message_messages',
            icon: 'comm-line_message',
          },
          {
            label: 'LINEオーディエンス一覧',
            page: `${rootPath}/a/crm/line/audience/list`,
            role: 'crm_line_message_items',
            icon: 'segment',
          },
        ],
      },
      {
        label: 'メール',
        role: 'crm_line_message_messages',
        children: [
          {
            label: 'メール一斉配信',
            page: `${rootPath}/a/crm/mail/list`,
            role: 'crm_line_message_messages',
            icon: 'mail',
          },
          {
            label: 'メール一斉配信設定',
            page: `${rootPath}/a/crm/mail/settings`,
            role: 'crm_line_message_messages',
            icon: 'setting',
          },
        ],
      },
    ],
  },
  {
    name: 'adminAnalysisMenu',
    label: '分析',
    type: 'menuList',
    data: [
      { label: 'パラメータ分析', icon: 'browser', page: `${rootPath}/a/analysis/qtm/list` },
      {
        label: 'クリック計測',
        icon: 'browser',
        page: `${rootPath}/a/analysis/click/list`,
      },
      {
        label: '流入経路',
        icon: 'browser',
        page: `${rootPath}/a/settings/system/inflow-route/list`,
      },
    ],
  },
  {
    name: 'adminSystemMenu',
    label: '設定',
    type: 'menuList',
    data: [
      {
        label: 'データ項目',
        role: 'settings_customer_data',
        children: [
          {
            label: '取得項目',
            role: 'settings_customer_data_items',
            icon: 'db',
            children: [
              { label: '一覧', page: `${rootPath}/a/settings/cdp/item/list` },
              { label: 'グループ', page: `${rootPath}/a/settings/cdp/item-group/list` },
            ],
          },
          {
            label: '顧客属性',
            role: 'settings_customer_data_tags',
            icon: 'tag',
            children: [
              { label: '一覧', page: `${rootPath}/a/settings/cdp/tag/list` },
              { label: 'グループ', page: `${rootPath}/a/settings/cdp/tag-group/list` },
            ],
          },
          {
            label: '顧客リスト表示設定',
            role: 'settings_customer_data_list',
            icon: 'user-list',
            page: `${rootPath}/a/settings/cdp/list-setting`,
          },
        ],
      },

      {
        label: 'データ収集',
        role: 'settings_customer_acquisition',
        children: [
          {
            label: 'アンケート',
            role: 'settings_customer_acquisition_enquete',
            icon: 'paper-enq',
            page: `${rootPath}/a/settings/acq/enq/list`,
          },
          {
            label: 'マイデータ',
            role: 'settings_customer_acquisition_mydata',
            icon: 'db-user-setting',
            page: `${rootPath}/a/settings/acq/mydata/list`,
          },
          {
            label: 'ヒアリングシート',
            role: 'settings_customer_service_type',
            icon: 'clipboard',
            page: `${rootPath}/a/settings/cs/type/list`,
          },
          {
            label: 'ユーザーアクション',
            role: 'settings_customer_service_reception',
            icon: 'flag',
            page: `${rootPath}/a/settings/cs/reception/list`,
          },
          {
            label: 'フォームテンプレート',
            role: 'settings_incentive_coupon',
            icon: 'forms',
            page: `${rootPath}/a/settings/incentive/coupon/template/list`,
          },
        ],
      },

      {
        label: 'セグメント',
        role: 'settings_customer_data_segment',
        children: [
          { label: '一覧', icon: 'role',page: `${rootPath}/a/settings/cdp/segment/list` },
          { label: 'グループ', icon: 'folder',page: `${rootPath}/a/settings/cdp/segment-group/list` },
        ],
      },

      {
        label: '顧客マイページ',
        role: 'settings_contents',
        children: [
          {
            label: 'マイページ設定',
            role: 'settings_system_mypage',
            icon: 'browser-mypage',
            page: `${rootPath}/a/settings/system/mypage`,
          },
          {
            label: 'リンク管理',
            role: 'settings_contents_links',
            icon: 'browser',
            page: `${rootPath}/a/settings/contents/links/list`,
          },
        ],
      },
      {
        label: 'コンテンツ',
        role: 'settings_system',
        children: [
          {
            label: 'ムードボード管理',
            icon: 'moodboard',
            role: 'settings_system_access',
            page: `${rootPath}/a/mb-stocks`,
          },
        ]
      },
      {
        label: 'LINE公式アカウント',
        role: '',
        children: [
          {
            label: '連携LINE公式アカウント',
            icon: 'line',
            page: `${rootPath}/a/settings/system/line-account/list`,
          },
          {
            label: 'メッセージテンプレート',
            page: `${rootPath}/a/crm/line/templates/list`,
            role: 'crm_line_message_messages',
            icon: 'display-tile',
          },
          {
            label: 'カードタイプメッセージ',
            page: `${rootPath}/a/crm/line/item/list`,
            role: 'crm_line_message_items',
            icon: 'carousel',
          },
          {
            label: 'リッチメッセージ',
            page: `${rootPath}/a/crm/line/image_message_item/list`,
            role: 'crm_line_message_items',
            icon: 'chat-image',
          },

          {
            label: 'リッチメニュー',
            icon: 'rich_menu',
            role: 'settings_incentive',
            children: [
              {
                label: 'アイテム',
                icon: 'rich_menu',
                page: `${rootPath}/a/settings/system/rich-menu-item/list`,
              },
              {
                label: 'タブ',
                icon: 'rich_menu',
                page: `${rootPath}/a/settings/system/rich-menu-tab/list`,
              },
              {
                label: '配信管理',
                icon: 'rich_menu_setting',
                page: `${rootPath}/a/settings/system/rich-menu-setting/list`,
              },
            ],
          },
          {
            label: '自動応答',
            icon: 'comm-auto_res',
            page: `${rootPath}/a/settings/system/line-auto-reply/list`,
          },
        ],
      },

      {
        label: 'インセンティブ',
        role: 'settings_incentive',
        children: [
          {
            label: 'ポイント',
            role: 'settings_customer_data_items',
            icon: 'point',
            page: `${rootPath}/a/settings/incentive/point/list`,
          },
          {
            label: 'ポイント特典',
            role: 'settings_customer_data_items',
            icon: 'point_bank',
            children: [
              {
                label: '一覧',
                role: 'settings_incentive_coupon',
                page: `${rootPath}/a/settings/incentive/coupon/list`,
              },
              {
                label: 'カテゴリ',
                role: 'settings_incentive_coupon',
                page: `${rootPath}/a/settings/incentive/coupon/category/list`,
              },
              {
                label: '利用履歴',
                role: 'settings_incentive_coupon',
                page: `${rootPath}/a/settings/incentive/coupon/history/list`,
              },
            ],
          },
          {
            label: 'クーポン',
            role: 'settings_incentive_mgr',
            icon: 'coupon',
            page: `${rootPath}/a/settings/incentive/mgr/list`,
          },
          {
            label: 'スタンプカード',
            role: 'settings_incentive_card',
            icon: 'stamp',
            page: `${rootPath}/a/settings/incentive/card/list`,
          },
          {
            label: '設定',
            role: 'settings_system_incentive',
            icon: 'browser-incentive',
            page: `${rootPath}/a/settings/system/incentive`,
          },
        ],
      },
      {
        label: '管理者ユーザー',
        role: 'settings_system',
        children: [
          { label: '一覧', icon:'user-admin', page: `${rootPath}/a/settings/system/admin-user/list` },
          { label: 'グループ', icon:'groups', page: `${rootPath}/a/settings/system/admin-user-group/list` },
          {
            label: 'ロール設定',
            role: 'settings_system_roles',
            icon: 'role',
            page: `${rootPath}/a/settings/system/role/list`,
          },
          {
            label: 'アクセス制限',
            role: 'settings_system_access',
            icon: 'security',
            page: `${rootPath}/a/settings/system/access`,
          },
        ],
      },
      {
        label: 'インポート/エクスポート',
        role: 'settings_system_data',
        children: [
          {
            label: 'データインポート',
            icon: 'db-import',
            page: `${rootPath}/a/settings/system/data/import`,
          },
          {
            label: 'データエクスポート',
            icon: 'db-export',
            page: `${rootPath}/a/settings/system/data/export`,
          },
          {
            label: 'データインポート設定',
            icon: 'setting',
            page: `${rootPath}/a/settings/system/data/import/setting`,
          },
        ],
      },
      {
        label: 'システム設定',
        role: 'settings_system',
        children: [
          {
            label: '共通設定',
            role: 'settings_system_common',
            icon: 'browser-system',
            page: `${rootPath}/a/settings/system/common`,
          },
          {
            label: '企業情報設定',
            role: 'settings_system_company',
            icon: 'browser-corporate',
            page: `${rootPath}/a/settings/system/company`,
          },
          {
            label: '管理画面設定',
            role: 'settings_system_admin',
            icon: 'browser-system',
            page: `${rootPath}/a/settings/system/admin`,
          },
        ],
      },
      {
        label: '契約',
        role: 'settings_system_users',
        children: [
          { label: '契約管理', icon:'support', page: `${rootPath}/a/settings/system/account/contract` },
          { label: 'クレジットカード変更', icon:'card', page: `${rootPath}/a/settings/system/account/card` },
        ] },
    ],},
];
