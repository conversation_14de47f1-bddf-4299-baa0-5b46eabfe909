const fs = require('fs');
const path = require('path');

module.exports = (target) => {
  // 対象ディレクトリが無ければスキップ
  if (!fs.existsSync(target)) return;


  const csvPath = target+'/tables'
  const distPath = target+'/dist'

  // distDirectoryが無ければ作る
  if (!fs.existsSync(distPath)) fs.mkdirSync(distPath);

  fs.readdir(csvPath, (err, files) => {
    if (err) {
      console.error(`error!::${err}`);
      return;
    }

    files.forEach(file => {
      // 拡張子チェック
      const parsedFile = path.parse(`${csvPath}/${file}`)
      const ext = parsedFile.ext;
      if( ext !== '.csv' ) return;

      const csv = fs.readFileSync(`${csvPath}/${file}`, 'utf-8');

      const rows = csv.split('\n');
      const headers = rows[0].split(',');
      const arrayOfObjects = rows.slice(1).map(row => {
        const values = row.split(',');
        const obj = {};
        headers.forEach((header, index) => {
          obj[header] = values[index];
        });
        return obj;
      });

      const strOutput = JSON.stringify(arrayOfObjects);

      const outputFilename = `${parsedFile.name}.js`;
      fs.writeFile(`${distPath}/${outputFilename}`, 'export default '+strOutput, (err) => {
        if(err) {
          console.log(`error!::${err}`);
          return;
        }
        console.log(`created::${outputFilename}`)
      });
    });
  })
}