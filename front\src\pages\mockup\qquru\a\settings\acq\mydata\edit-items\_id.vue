<template>
  <StaticsLayoutMain type="edit">
    <StaticsTrunkPageTitle
      label="マイデータページの設定"
    />
    <StaticsLayoutSection type="boxed_content" label="" :styles="{ height: 'maxContent' }">
      <StaticsLayoutCmsEditor>
        <StaticsTrunkEditorRowBox type="cEdH1">
          <h1 class="cEdH1">見出し1</h1>
        </StaticsTrunkEditorRowBox>
        <StaticsTrunkEditorRowBox type="cEdText">
          <p class="cEdText">テキストテキストテキストテキストテキストテキストテキストテキストテキストテキストテキストテキストテキストテキストテキストテキストテキストテキストテキストテキスト</p>
        </StaticsTrunkEditorRowBox>
        <StaticsTrunkEditorRowBox type="cEdInputItem">
          <StaticsTrunkEditorBlock icon="sys-form" label="お名前" subLabel="（名前）" editLabel :toggles="[{label:'必須', link:'#'}]" />
        </StaticsTrunkEditorRowBox>
        <StaticsTrunkEditorRowBox type="cEdInputItem">
          <StaticsTrunkEditorBlock icon="sys-form" label="住所" editLabel :toggles="[{label:'必須', link:'#'}]" />
        </StaticsTrunkEditorRowBox>
        <StaticsTrunkEditorRowBox type="cEdInputItem">
          <StaticsTrunkEditorBlock icon="sys-form" label="電話番号" editLabel :toggles="[{label:'必須', link:'#'}]" />
        </StaticsTrunkEditorRowBox>
        <StaticsTrunkEditorRowBox type="cEdHtml">
          <StaticsTrunkInputGroup type="textarea" textareaType="wysiwyg" />
        </StaticsTrunkEditorRowBox>
        <StaticsTrunkEditorRowBox type="cEdInputItem">
          <StaticsTrunkEditorBlock icon="sys-form" label="取得項目名" :toggles="[{label:'初期表示', link:'#'}]" />
        </StaticsTrunkEditorRowBox>
      </StaticsLayoutCmsEditor>
    </StaticsLayoutSection>
    
    <div class="bottomFixedActions">
      <StaticsAggregationActionGroup
        :actions="[
          { type:'primary', label: '保存する', link: getMockupRoot()+'/a/settings/acq/mydata/list' },
        ]"
      />
    </div>
  </StaticsLayoutMain>
</template>
<script>
import chamomileEditors from '~/mixins/chamomile/editors';
import mockupMixin from '~/mixins/mockup/common.js';

export default {
  layout: 'chamomile/admin',
  mixins: [chamomileEditors, mockupMixin],
    data() {
      return {
        toggleName: null,
      };
    },
  methods: {
    // Modal : callback値を
    openModal() {this.$refs.modal.showModal();},
    openModalQr() {this.$refs.modalQr.showModal();},
  },
};
</script>
<style lang="scss" scoped>
@use "~/mixins/chamomile/scss/index" as *;
</style>