<?xml version="1.0" encoding="UTF-8"?>
<svg id="_レイヤー_2" data-name="レイヤー 2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 64 64">
  <defs>
    <style>
      .cls-1 {
        fill: #2e2f2e;
      }

      .cls-1, .cls-2 {
        stroke-width: 0px;
      }

      .cls-3 {
        stroke-width: 2.5px;
      }

      .cls-3, .cls-2, .cls-4 {
        fill: none;
      }

      .cls-3, .cls-4 {
        stroke: #2e2f2e;
        stroke-miterlimit: 10;
      }

      .cls-4 {
        stroke-width: 3px;
      }
    </style>
  </defs>
  <g id="icon-lined">
    <g id="db-export">
      <rect class="cls-2" width="64" height="64"/>
      <ellipse class="cls-4" cx="22.04" cy="11.55" rx="12.89" ry="2.96"/>
      <path class="cls-4" d="M9.16,22.18s2.22,2.96,12.89,2.96,12.89-2.96,12.89-2.96"/>
      <circle class="cls-3" cx="14.61" cy="19.41" r="1.98"/>
      <circle class="cls-3" cx="14.61" cy="30.07" r="1.98"/>
      <path class="cls-4" d="M34.72,29.24h12.12c4.42,0,8,3.58,8,8v16.18c0,1.1-.9,2-2,2h-18.12c-1.1,0-2-.9-2-2v-22.18c0-1.1.9-2,2-2Z"/>
      <path class="cls-4" d="M32.72,31.24c0-1.1.9-2,2-2h.21V12.34h-.48c-1.5,1.25-6.48,2.17-12.41,2.17s-10.91-.92-12.41-2.17h-.48v20.52c0,1.64,5.77,2.96,12.89,2.96,4.44,0,8.36-.52,10.68-1.3v-3.28Z"/>
      <polyline class="cls-4" points="47.65 29.24 47.65 36.43 54.84 36.43"/>
      <polyline class="cls-4" points="17.29 39.36 17.29 48.05 26.79 48.05"/>
      <polygon class="cls-1" points="29.19 48.05 23.68 43.82 23.68 52.28 29.19 48.05"/>
    </g>
  </g>
</svg>