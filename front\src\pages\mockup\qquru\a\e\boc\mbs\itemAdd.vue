<template>
  <StaticsLayoutCustomer type="preset" :styles="{view:'body'}">
    <StaticsLayoutMain type="view">
      <StaticsTrunkPageTitle
        label="アイテムを追加"
        :back="true"
        :backLink="getMockupRoot()+'/a/e/boc/mbs/0001/01/view'"
      />
      <StaticsLayoutSection type="default_content">
        <StaticsTrunkTabs
            :tabs="[
            { label: 'すべて' },
            { label: 'リビング' },
            { label: 'ダイニング' },
            { label: 'ベッドルーム' },
            { label: 'ホームオフィス' },
            ]"
        />
        <p class="text">気になるアイテムのお気に入りアイコンをタップすることでムードボードに登録されます。</p>
        <StaticsAggregationCardList
          column="hor-3"
          type="default"
          :styles="{}"
          childType="default"
          :childStyles="{}"
          :cards="[
            { type:'plain', thumbnail: 'dummy/moodboard/01.jpg' },
            { type:'plain', thumbnail: 'dummy/moodboard/02.jpg' },
            { type:'plain', thumbnail: 'dummy/moodboard/03.jpg' },
            { type:'plain', thumbnail: 'dummy/moodboard/04.jpg' },
            { type:'plain', thumbnail: 'dummy/moodboard/05.jpg' },
            { type:'plain', thumbnail: 'dummy/moodboard/06.jpg' },
            { type:'plain', thumbnail: 'dummy/moodboard/07.jpg' },
            { type:'plain', thumbnail: 'dummy/moodboard/08.jpg' },
            { type:'plain', thumbnail: 'dummy/moodboard/09.jpg' },
            { type:'plain', thumbnail: 'dummy/moodboard/10.jpg' },
            { type:'plain', thumbnail: 'dummy/moodboard/11.jpg' },
            { type:'plain', thumbnail: 'dummy/moodboard/12.jpg' },
            { type:'plain', thumbnail: 'dummy/moodboard/13.jpg' },
            { type:'plain', thumbnail: 'dummy/moodboard/14.jpg' },
            { type:'plain', thumbnail: 'dummy/moodboard/15.jpg' },
          ]"
        >
          <template v-slot:list0>
            <div style="position: relative;">
              <StaticsTrunkCard type="plain" thumbnail="dummy/moodboard/01.jpg" />
              <button @click="onHeartClick(1)" style="position: absolute; bottom: 8px; right: 8px; background: rgba(255,255,255,0.9); border: none; border-radius: 50%; width: 32px; height: 32px; display: flex; align-items: center; justify-content: center; cursor: pointer;">
                <StaticsElementIcon icon="heart" style="width: 20px; height: 20px;" />
              </button>
            </div>
          </template>
          <template v-slot:list1>
            <div style="position: relative;">
              <StaticsTrunkCard type="plain" thumbnail="dummy/moodboard/02.jpg" />
              <button @click="onHeartClick(2)" style="position: absolute; bottom: 8px; right: 8px; background: rgba(255,255,255,0.9); border: none; border-radius: 50%; width: 32px; height: 32px; display: flex; align-items: center; justify-content: center; cursor: pointer;">
                <StaticsElementIcon icon="heart" style="width: 20px; height: 20px;" />
              </button>
            </div>
          </template>
          <template v-slot:list2>
            <div style="position: relative;">
              <StaticsTrunkCard type="plain" thumbnail="dummy/moodboard/03.jpg" />
              <button @click="onHeartClick(3)" style="position: absolute; bottom: 8px; right: 8px; background: rgba(255,255,255,0.9); border: none; border-radius: 50%; width: 32px; height: 32px; display: flex; align-items: center; justify-content: center; cursor: pointer;">
                <StaticsElementIcon icon="heart" style="width: 20px; height: 20px;" />
              </button>
            </div>
          </template>
          <template v-slot:list3>
            <div style="position: relative;">
              <StaticsTrunkCard type="plain" thumbnail="dummy/moodboard/04.jpg" />
              <button @click="onHeartClick(4)" style="position: absolute; bottom: 8px; right: 8px; background: rgba(255,255,255,0.9); border: none; border-radius: 50%; width: 32px; height: 32px; display: flex; align-items: center; justify-content: center; cursor: pointer;">
                <StaticsElementIcon icon="heart" style="width: 20px; height: 20px;" />
              </button>
            </div>
          </template>
          <template v-slot:list4>
            <div style="position: relative;">
              <StaticsTrunkCard type="plain" thumbnail="dummy/moodboard/05.jpg" />
              <button @click="onHeartClick(5)" style="position: absolute; bottom: 8px; right: 8px; background: rgba(255,255,255,0.9); border: none; border-radius: 50%; width: 32px; height: 32px; display: flex; align-items: center; justify-content: center; cursor: pointer;">
                <StaticsElementIcon icon="heart" style="width: 20px; height: 20px;" />
              </button>
            </div>
          </template>
          <template v-slot:list5>
            <div style="position: relative;">
              <StaticsTrunkCard type="plain" thumbnail="dummy/moodboard/06.jpg" />
              <button @click="onHeartClick(6)" style="position: absolute; bottom: 8px; right: 8px; background: rgba(255,255,255,0.9); border: none; border-radius: 50%; width: 32px; height: 32px; display: flex; align-items: center; justify-content: center; cursor: pointer;">
                <StaticsElementIcon icon="heart" style="width: 20px; height: 20px;" />
              </button>
            </div>
          </template>
          <template v-slot:list6>
            <div style="position: relative;">
              <StaticsTrunkCard type="plain" thumbnail="dummy/moodboard/07.jpg" />
              <button @click="onHeartClick(7)" style="position: absolute; bottom: 8px; right: 8px; background: rgba(255,255,255,0.9); border: none; border-radius: 50%; width: 32px; height: 32px; display: flex; align-items: center; justify-content: center; cursor: pointer;">
                <StaticsElementIcon icon="heart" style="width: 20px; height: 20px;" />
              </button>
            </div>
          </template>
          <template v-slot:list7>
            <div style="position: relative;">
              <StaticsTrunkCard type="plain" thumbnail="dummy/moodboard/08.jpg" />
              <button @click="onHeartClick(8)" style="position: absolute; bottom: 8px; right: 8px; background: rgba(255,255,255,0.9); border: none; border-radius: 50%; width: 32px; height: 32px; display: flex; align-items: center; justify-content: center; cursor: pointer;">
                <StaticsElementIcon icon="heart" style="width: 20px; height: 20px;" />
              </button>
            </div>
          </template>
          <template v-slot:list8>
            <div style="position: relative;">
              <StaticsTrunkCard type="plain" thumbnail="dummy/moodboard/09.jpg" />
              <button @click="onHeartClick(9)" style="position: absolute; bottom: 8px; right: 8px; background: rgba(255,255,255,0.9); border: none; border-radius: 50%; width: 32px; height: 32px; display: flex; align-items: center; justify-content: center; cursor: pointer;">
                <StaticsElementIcon icon="heart" style="width: 20px; height: 20px;" />
              </button>
            </div>
          </template>
          <template v-slot:list9>
            <div style="position: relative;">
              <StaticsTrunkCard type="plain" thumbnail="dummy/moodboard/10.jpg" />
              <button @click="onHeartClick(10)" style="position: absolute; bottom: 8px; right: 8px; background: rgba(255,255,255,0.9); border: none; border-radius: 50%; width: 32px; height: 32px; display: flex; align-items: center; justify-content: center; cursor: pointer;">
                <StaticsElementIcon icon="heart" style="width: 20px; height: 20px;" />
              </button>
            </div>
          </template>
          <template v-slot:list10>
            <div style="position: relative;">
              <StaticsTrunkCard type="plain" thumbnail="dummy/moodboard/11.jpg" />
              <button @click="onHeartClick(11)" style="position: absolute; bottom: 8px; right: 8px; background: rgba(255,255,255,0.9); border: none; border-radius: 50%; width: 32px; height: 32px; display: flex; align-items: center; justify-content: center; cursor: pointer;">
                <StaticsElementIcon icon="heart" style="width: 20px; height: 20px;" />
              </button>
            </div>
          </template>
          <template v-slot:list11>
            <div style="position: relative;">
              <StaticsTrunkCard type="plain" thumbnail="dummy/moodboard/12.jpg" />
              <button @click="onHeartClick(12)" style="position: absolute; bottom: 8px; right: 8px; background: rgba(255,255,255,0.9); border: none; border-radius: 50%; width: 32px; height: 32px; display: flex; align-items: center; justify-content: center; cursor: pointer;">
                <StaticsElementIcon icon="heart" style="width: 20px; height: 20px;" />
              </button>
            </div>
          </template>
          <template v-slot:list12>
            <div style="position: relative;">
              <StaticsTrunkCard type="plain" thumbnail="dummy/moodboard/13.jpg" />
              <button @click="onHeartClick(13)" style="position: absolute; bottom: 8px; right: 8px; background: rgba(255,255,255,0.9); border: none; border-radius: 50%; width: 32px; height: 32px; display: flex; align-items: center; justify-content: center; cursor: pointer;">
                <StaticsElementIcon icon="heart" style="width: 20px; height: 20px;" />
              </button>
            </div>
          </template>
          <template v-slot:list13>
            <div style="position: relative;">
              <StaticsTrunkCard type="plain" thumbnail="dummy/moodboard/14.jpg" />
              <button @click="onHeartClick(14)" style="position: absolute; bottom: 8px; right: 8px; background: rgba(255,255,255,0.9); border: none; border-radius: 50%; width: 32px; height: 32px; display: flex; align-items: center; justify-content: center; cursor: pointer;">
                <StaticsElementIcon icon="heart" style="width: 20px; height: 20px;" />
              </button>
            </div>
          </template>
          <template v-slot:list14>
            <div style="position: relative;">
              <StaticsTrunkCard type="plain" thumbnail="dummy/moodboard/15.jpg" />
              <button @click="onHeartClick(15)" style="position: absolute; bottom: 8px; right: 8px; background: rgba(255,255,255,0.9); border: none; border-radius: 50%; width: 32px; height: 32px; display: flex; align-items: center; justify-content: center; cursor: pointer;">
                <StaticsElementIcon icon="heart" style="width: 20px; height: 20px;" />
              </button>
            </div>
          </template>
        </StaticsAggregationCardList>
      </StaticsLayoutSection>

      <!-- <StaticsLayoutSection type="boxed_content" label="アイテムをタップした際の挙動" toggle>
        <StaticsAggregationActionGroup
        :actions="[
          { type: 'primary', label: 'モーダルを開く', callback: openModal },
        ]"
        />
        <StaticsLayoutModal ref="modal" label="お気に入りに登録" actionLabel="登録する" closeLabel="キャンセル">
          <ul class="inputGroups">
            <li>
              <StaticsElementImage src = "dummy/moodboard/15.jpg" alt = "" type = "default" style = "width:640px" />
            </li>
            <li>
              <StaticsTrunkSelectiveInputGroup
                required
                label=""
                inputType="button"
                :options="[
                  { label: '全体の雰囲気', value: 'select1' },
                  { label: '色合い', value: 'select2'},
                  { label: '特定の家具', value: 'select3' },
                  { label: 'その他', value: 'select4' },
                ]"
                v-model="byRadio"
              >
                <template v-slot:[`inner-select3`]>
                  <StaticsTrunkInputGroup
                    type="radio"
                    inputType="button"
                    name="radio1"
                    required
                    :options="[
                      {label:'テーブル', value: 1},
                      {label:'チェア', value: 2},
                      {label:'収納', value: 3},
                      {label:'ラグ', value: 4},
                      {label:'ランプ', value: 5},
                      {label:'アクセサリ・小物', value: 6},
                    ]"
                  />
                </template>
                <template v-slot:[`inner-select4`]>
                  <ul class="inputGroups">
                    <li>
                      <StaticsTrunkInputGroup
                        type="textarea"
                        textareaType="default"
                        placeholder="お気に入りのポイントを入力"
                        emojiPicker
                        required
                      />
                    </li>
                  </ul>
                </template>
              </StaticsTrunkSelectiveInputGroup>
            </li>
          </ul>
        </StaticsLayoutModal>
      </StaticsLayoutSection> -->

      <StaticsLayoutSection type="default" :styles="{ height: 'maxContent' }"></StaticsLayoutSection>

      <div class="bottomFixedActions">
        <StaticsAggregationActionGroup
          :actions="[
            { type:'primary', label: 'ムードボードへ', link:getMockupRoot()+'/a/e/boc/mbs/0001/01/view' },
          ]"
        />
      </div>
    </StaticsLayoutMain>
  </StaticsLayoutCustomer>
</template>
<script>
import chamomileEditors from '~/mixins/chamomile/editors';
import mockupMixin from '~/mixins/mockup/common.js';

export default {
  layout: 'chamomile/admin',
  mixins: [chamomileEditors, mockupMixin],
    data() {
      return {
        toggleName: null,
      };
    },
  methods: {
    // Modal : callback値を
    openModal() {this.$refs.modal.showModal();},
    openModalQr() {this.$refs.modalQr.showModal();},
    onHeartClick(itemIndex) {
      console.log(`❤️ Heart clicked for item ${itemIndex}`);
      // Có thể thêm logic để thêm vào moodboard ở đây
    },
  },
};
</script>
<style lang="scss" scoped>
@use "~/mixins/chamomile/scss/index" as *;
</style>
