<template>
  <StaticsLayoutMain type="edit">
    <StaticsTrunkPageTitle
      label="取得項目の編集"
      back="true"
    />

    <StaticsLayoutSection type="boxed_content_bold">
      <ul class="inputGroups">
        <li> <StaticsTrunkInputGroup label="取得項目名" type="text_hol" width="r20" required="true" bottomText="取得項目名は顧客マイページや接客時の項目名として利用されます。" /> </li>
        <li> <StaticsTrunkInputGroup label="取得項目グループ" type="select_hol" width="r20" required="true" /> </li>
        <li> <StaticsTrunkInputGroup label="説明" type="text_hol" width="w100" /> </li>
        <li> <StaticsTrunkInputGroup label="取得項目タイプの戦隊" type="select_hol" width="r20" required="true" bottomText="取得項目タイプは一度選択すると変更はできません。" /> </li>
      </ul>
    </StaticsLayoutSection>
    <StaticsLayoutSection type="boxed_content" label="表示設定">
      <ul class="inputGroups">
        <li><StaticsTrunkInputGroup label="入力ボックスサイズ" type="radio" inputType="vertical" /></li>
        <li><StaticsTrunkInputGroup label="初期表示文字" type="text" width="r20" bottomText="テキストが入力されていない場合に表示されます" /></li>
        <li><StaticsTrunkInputGroup label="入力ボックス 前テキスト" type="text" width="r20" bottomText="" /></li>
        <li><StaticsTrunkInputGroup label="入力ボックス 後テキスト" type="text" width="r20" /></li>
        <li><StaticsTrunkInputGroup label="入力ボックス 上部テキスト" type="text" width="w100" bottomText="" /></li>
        <li><StaticsTrunkInputGroup label="入力ボックス 下部テキスト" type="text" width="w100" /></li>
      </ul>
    </StaticsLayoutSection>
    <StaticsLayoutSection type="boxed_content" label="入力制限">
      <ul class="inputGroups">
        <li><StaticsTrunkInputGroup label="文字数制限" type="radio" inputType="vertical" /></li>
        <li><StaticsTrunkInputGroup label="入力制限" topText="電話番号、メールアドレス、日付の入力制限は取得項目タイプの選択より各入力形式をご選択ください。" type="radio" inputType="vertical" /></li>
      </ul>
    </StaticsLayoutSection>


    <div class="bottomFixedActions">
      <StaticsAggregationActionGroup
        :actions="[
          { type:'primary', label: '保存する' },
        ]"
      />
    </div>
  </StaticsLayoutMain>
</template>
<script>
import mockupMixin from '~/mixins/mockup/common.js'
export default {
    layout: 'chamomile/admin',
  mixins: [mockupMixin],
};
</script>
<style lang="scss" scoped>
</style>
