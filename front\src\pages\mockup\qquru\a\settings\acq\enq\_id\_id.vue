<template>
  <StaticsLayoutMain type="edit">
    <StaticsTrunkPageTitle
      label="[顧客名]の回答内容"
      back
    />

    <StaticsLayoutSection type="boxed_content">
      <ul class="outputGroups bordered">
        <li><StaticsTrunkOutputGroup label="顧客名" type="text_hol" value="小川 徹" /></li>
        <li><StaticsTrunkOutputGroup label="回答日時" type="text_hol" value="2025年12月12日 12:12:12" /></li>
      </ul>
    </StaticsLayoutSection>

    <StaticsLayoutSection type="boxed_content" label="回答内容">
      <ul class="outputGroups bordered">
        <li><StaticsTrunkOutputGroup label="取得項目" type="text_hol" value="回答テキスト" /></li>
        <li><StaticsTrunkOutputGroup label="取得項目" type="text_hol" value="回答テキスト回答テキスト回答テキスト回答テキスト回答テキスト回答テキスト回答テキスト回答テキスト回答テキスト" /></li>
        <li><StaticsTrunkOutputGroup label="取得項目" type="text_hol" value="回答テキスト回答テキスト回答テキスト回答テキスト回答テキスト回答テキスト回答テキスト回答テキスト回答テキスト" /></li>
        <li><StaticsTrunkOutputGroup label="取得項目" type="text_hol" value="回答テキスト回答テキスト回答テキスト回答テキスト回答テキスト回答テキスト回答テキスト回答テキスト回答テキスト" /></li>
        <li><StaticsTrunkOutputGroup label="取得項目" type="text_hol" value="回答テキスト回答テキスト回答テキスト回答テキスト回答テキスト回答テキスト回答テキスト回答テキスト回答テキスト" /></li>
        <li><StaticsTrunkOutputGroup label="取得項目" type="text_hol" value="回答テキスト回答テキスト回答テキスト回答テキスト回答テキスト回答テキスト回答テキスト回答テキスト回答テキスト" /></li>
        <li><StaticsTrunkOutputGroup label="取得項目" type="text_hol" value="回答テキスト回答テキスト回答テキスト回答テキスト回答テキスト回答テキスト回答テキスト回答テキスト回答テキスト" /></li>
      </ul>
    </StaticsLayoutSection>

  </StaticsLayoutMain>
</template>
<script>
import chamomileEditors from '~/mixins/chamomile/editors';
import mockupMixin from '~/mixins/mockup/common.js';

export default {
  layout: 'chamomile/admin',
  mixins: [chamomileEditors, mockupMixin],
    data() {
      return {
        toggleName: null,
      };
    },
  methods: {
    // Modal : callback値を
    openModal() {this.$refs.modal.showModal();},
    openModalQr() {this.$refs.modalQr.showModal();},
  },
};
</script>
<style lang="scss" scoped>
@use "~/mixins/chamomile/scss/index" as *;
</style>
