<?xml version="1.0" encoding="UTF-8"?>
<svg id="_レイヤー_2" data-name="レイヤー 2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 64 64">
  <defs>
    <style>
      .cls-1 {
        fill: #2e2f2e;
      }

      .cls-1, .cls-2 {
        stroke-width: 0px;
      }

      .cls-3 {
        stroke-width: 2.5px;
      }

      .cls-3, .cls-2, .cls-4 {
        fill: none;
      }

      .cls-3, .cls-4 {
        stroke: #2e2f2e;
        stroke-miterlimit: 10;
      }

      .cls-4 {
        stroke-width: 3px;
      }
    </style>
  </defs>
  <g id="icon-lined">
    <g id="db-import">
      <rect class="cls-2" width="64" height="64"/>
      <polyline class="cls-4" points="23.09 9.51 23.09 17.05 30.62 17.05"/>
      <path class="cls-4" d="M42.42,32.67c-6.21,0-11.43-.97-13-2.28h-.5v21.49c0,1.71,6.04,3.11,13.5,3.11s13.5-1.39,13.5-3.11v-21.49h-.5c-1.58,1.31-6.79,2.28-13,2.28Z"/>
      <ellipse class="cls-4" cx="42.42" cy="29.56" rx="13.5" ry="3.11"/>
      <path class="cls-4" d="M28.92,30.39h.5c-.32-.26-.5-.54-.5-.83,0-.65.86-1.24,2.32-1.74v-10.81c0-4.42-3.58-8-8-8h-13.17c-1.1,0-2,.9-2,2v23.42c0,1.1.9,2,2,2h18.85v-6.04Z"/>
      <path class="cls-4" d="M28.92,40.69s2.32,3.11,13.5,3.11,13.5-3.11,13.5-3.11"/>
      <circle class="cls-3" cx="34.64" cy="37.79" r="2.07"/>
      <circle class="cls-3" cx="34.64" cy="48.97" r="2.07"/>
      <polyline class="cls-4" points="13.43 40.69 13.43 49.8 23.38 49.8"/>
      <polygon class="cls-1" points="25.89 49.8 20.12 45.37 20.12 54.23 25.89 49.8"/>
    </g>
  </g>
</svg>